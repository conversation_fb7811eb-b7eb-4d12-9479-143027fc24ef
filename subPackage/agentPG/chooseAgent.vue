<template>
  <view class="choose-agent-page">
    <view class="process-content">
      <view>
        <view class="form-item">
          <view class="label">选择代理商</view>
          <view class="select-input">
            <input class="select-text" placeholder="请选择" disabled :value="modelValue" />
            <van-icon color="#8D9094" name="arrow-down" />
          </view>
          <view class="label" style="margin-top: 20rpx;">选择医院</view>
          <view class="select-input">
            <input class="select-text" placeholder="请选择" disabled :value="modelValue" />
            <van-icon color="#8D9094" name="arrow-down" />
          </view>
        </view>
      </view>
    </view>
    <view class="order-btn" @click="handleLaunch">
      开始评估
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

const modelValue = ref('');
const remarkValue = ref('');

const handleLaunch = () => {
  
}
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.process-content {
  background: #ffffff;
  margin: 20rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  .form-item {
    margin-bottom: 20rpx;
    .label {
      margin-bottom: 12rpx;
      font-weight: 500;
      font-family: "PingFang SC";
      font-style: normal;
      font-size: 28rpx;
      color: #2F3133;
    }
  }
  .form-item:last-child {
    margin-bottom: 0;
  }
  .grouped-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    //border-bottom: 1px solid #E6EBF0;
    input {
      flex: 1;
      margin-left: 20rpx;
      border: none;
      outline: none;
      background: none;
    }
  }
  .select-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 64rpx;
    padding: 0 24rpx;
    border: 1rpx solid #e8e8e8;
    border-radius: 12rpx;
    background: #fff;
    box-sizing: border-box;
    cursor: pointer;

    .select-text {
      font-size: 26rpx;
      color: #333;
      flex: 1;

      &.placeholder {
        color: #999;
      }
    }

    .select-arrow {
      font-size: 20rpx;
      color: #999;
      transform: rotate(0deg);
      transition: transform 0.3s ease;
    }

    &:active {
      background: #f8f9fa;
    }
  }
  .remark {
    margin-top: 14rpx;
  }
}
.order-btn {
  position: fixed;
  z-index: 999;
  bottom: 170rpx;
  width: 712rpx;
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 8rpx;
  background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
  @include setlightFont(24rpx, 64rpx, #fff);
  text-align: center;
}
</style>